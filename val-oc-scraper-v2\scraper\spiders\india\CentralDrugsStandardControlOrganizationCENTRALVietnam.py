from scraper.OCSpider import OCSpider
import dateparser
from datetime import datetime
from urllib.parse import urljoin


class CentralDrugsStandardControlOrganizationCENTRALVietnam(OCSpider):
    name = "CentralDrugsStandardControlOrganizationCENTRALVietnam"

    start_urls_names = {
        "https://cdsco.gov.in/opencms/opencms/en/PvPI/": "News",
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "India"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kolkata"

    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//table[@id="example"]/tbody/tr')
        for row in rows:
            pdf_url = row.xpath('./td[4]/a/@href').get()
            title = row.xpath('./td[2]/text()').get()
            date_text = row.xpath('./td[3]/text()').get()
            if pdf_url:
                full_pdf_url = urljoin("https://cdsco.gov.in", pdf_url)
                self.article_data_map[full_pdf_url] = {
                    "title": title.strip() if title else "PDF Document",
                    "curr_date": date_text,
                    "full_url": full_pdf_url
                }
                articles.append(full_pdf_url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "PDF Document")

    def get_body(self, response, entry=None) -> str:
        return ""

    def get_images(self, response, entry=None) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        entry_url = response.request.meta.get('entry')
        raw_date = self.article_data_map.get(entry_url, {}).get("curr_date", "")
        if not raw_date:
            return datetime.now().strftime(self.date_format())
        clean_text = raw_date.replace("\xa0", " ").strip()
        date_obj = dateparser.parse(clean_text, languages=['en'])
        if date_obj:
            return date_obj.strftime(self.date_format())
        return datetime.now().strftime(self.date_format())

    def get_authors(self, response, entry=None):
        return []

    def get_document_urls(self, response, entry=None):
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None

    def get_pdf(self, response, entry=None):
        return None

    def get_meta(self, response) -> list:
        return []
