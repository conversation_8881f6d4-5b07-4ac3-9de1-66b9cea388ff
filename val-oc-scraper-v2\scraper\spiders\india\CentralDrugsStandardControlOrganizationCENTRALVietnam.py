from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
from urllib.parse import urljoin


class CentralDrugsStandardControlOrganizationCENTRALVietnam(OCSpider):
    name = "CentralDrugsStandardControlOrganizationCENTRALVietnam"

    start_urls_names = {
        "https://cdsco.gov.in/opencms/opencms/en/PvPI/": "News",
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "India"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kolkata"

    article_data_map = {}

    def get_articles(self, response) -> list:
        rows = response.xpath('//table[@id="example"]/tbody/tr')
        article_urls = []
        for i, row in enumerate(rows, start=1):
            pdf_url = row.xpath('./td[4]/a/@href').get()
            date_text = row.xpath('./td[3]/text()').get()
            if pdf_url:
                full_pdf_url = urljoin("https://cdsco.gov.in", pdf_url)
                article_urls.append(full_pdf_url)
                self.article_data_map[full_pdf_url] = {
                    "curr_date": date_text
                }
        return article_urls

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        if response.url.lower().endswith(".pdf") or "download_file_division.jsp" in response.url:
            filename = response.url.split("/")[-1]
            return filename if filename else "PDF Document"
        title = response.xpath('//h1//text() | //title//text()').get()
        if title:
            return title.strip()
        return response.url
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        entry_url = response.request.meta.get('entry')
        raw_date = self.article_data_map.get(entry_url, {}).get("curr_date", "")
        if not raw_date:
            return datetime.now().strftime(self.date_format())
        clean_text = raw_date.replace("\xa0", " ").strip()
        date_obj = dateparser.parse(clean_text, languages=['en'])
        if date_obj:
            return date_obj.strftime("%Y-%m-%d")
        return datetime.now().strftime(self.date_format())

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [entry] if entry else []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None